<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light only">
    <meta name="theme-color" content="#ffffff">
    <meta name="msapplication-navbutton-color" content="#ffffff">
    <meta name="apple-mobile-web-app-status-bar-style" content="light-content">
    <meta name="supported-color-schemes" content="light">
    <title th:text="${pageTitle}">视频播放</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">




    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/play-style.css" rel="stylesheet">




    </style>
</head>
<body style="background-color: #ffffff !important; color: #333333 !important; color-scheme: light only !important;">
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <!-- 导航链接 -->
                <div class="d-flex align-items-center">
                    <ul class="navbar-nav d-flex flex-row mb-0">
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/">
                                <i class="fas fa-home me-1"></i>首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/videos">
                                <i class="fas fa-video me-1"></i>视频
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/admin">
                                <i class="fas fa-cog me-1"></i>管理
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 搜索框 -->
                <div class="search-container">
                    <form class="d-flex search-form" action="/search" method="get">
                        <input class="form-control me-2 search-input-mobile" type="search" name="keyword" placeholder="搜索...">
                        <button class="btn btn-outline-light btn-sm" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="btn btn-outline-light btn-sm ms-1" type="button" onclick="toggleSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                    </form>
                    <button class="btn btn-outline-light btn-sm search-toggle" onclick="toggleSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container-fluid px-3 py-4">
        <div class="row justify-content-center">
            <!-- 视频播放区域 -->
            <div class="col-12 col-md-8 col-lg-6">
                <!-- 视频播放器容器 -->
                <div class="video-player-container mb-4">
                    <div class="video-wrapper">
                        <!-- 优化内联播放的视频播放器 -->
                        <video
                            id="video-player"
                            controls
                            preload="metadata"
                            playsinline
                            webkit-playsinline
                            x5-video-player-type="h5"
                            x5-video-player-fullscreen="true"
                            x5-video-orientation="portraint"
                            th:data-video-url="${video.videoUrl}"
                            th:data-video-id="${video.id}"
                            th:poster="${video.thumbnailUrl}">
                            <source th:src="${video.videoUrl}" type="video/mp4">
                            您的浏览器不支持HTML5视频播放。
                        </video>
                    </div>
                </div>

                <!-- 视频信息 -->
                <div class="video-info bg-white rounded-3 shadow-sm p-4 mb-4">
                    <h1 class="video-title h4 mb-3" th:text="${video.title}">视频标题</h1>
                    <div class="video-stats">
                        <time class="video-date text-muted" th:text="${#temporals.format(video.createdTime, 'yyyy-MM-dd')}">2024-01-01</time>
                    </div>
                    <!-- 视频描述 -->
                    <!-- <div class="video-description mt-3" th:if="${video.description}">
                        <p class="text-muted mb-0" th:text="${video.description}">视频描述</p>
                    </div> -->
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <!-- <h5><i class="fas fa-play-circle me-2"></i>佳茵轻康</h5> -->
                    <p class="mb-0">轻康自然，享瘦生活。</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <small>© 2025 加盟合作. <a href="/about" class="text-light text-decoration-none">点这里：联系我们</a></small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>


    <!-- 自定义JS -->
    <script src="/js/main.js"></script>


    <script th:inline="javascript">
        // 优化内联播放的HTML5视频播放器
        document.addEventListener('DOMContentLoaded', function() {
            const videoElement = document.getElementById('video-player');
            const videoUrl = videoElement.dataset.videoUrl;
            const videoId = videoElement.dataset.videoId;

            // 设置视频源
            if (videoUrl) {
                videoElement.src = videoUrl;
            }

            // 优化内联播放设置
            function optimizeInlinePlayback() {
                // 确保内联播放属性设置正确
                videoElement.setAttribute('playsinline', 'true');
                videoElement.setAttribute('webkit-playsinline', 'true');

                // 移动端优化
                if (/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)) {
                    // iOS Safari 内联播放优化
                    videoElement.setAttribute('webkit-playsinline', 'true');
                    videoElement.setAttribute('playsinline', 'true');

                    // Android 微信/QQ浏览器内联播放优化
                    videoElement.setAttribute('x5-video-player-type', 'h5');
                    videoElement.setAttribute('x5-video-player-fullscreen', 'true');
                    videoElement.setAttribute('x5-video-orientation', 'portraint');

                    // 禁用自动全屏
                    videoElement.setAttribute('webkit-playsinline', 'true');
                    videoElement.muted = false; // 确保不是静音状态
                }

                // 设置视频尺寸适应容器
                videoElement.style.width = '100%';
                videoElement.style.height = 'auto';
                videoElement.style.objectFit = 'contain';
            }

            // 应用内联播放优化
            optimizeInlinePlayback();

            // 生成视频首帧缩略图
            function generateThumbnail() {
                if (!videoElement.poster) {
                    videoElement.addEventListener('loadeddata', function() {
                        try {
                            const canvas = document.createElement('canvas');
                            const ctx = canvas.getContext('2d');

                            // 设置画布尺寸
                            canvas.width = videoElement.videoWidth;
                            canvas.height = videoElement.videoHeight;

                            // 绘制视频首帧
                            ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

                            // 转换为base64图片
                            const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.8);
                            videoElement.poster = thumbnailDataUrl;

                            console.log('视频首帧缩略图生成成功');
                        } catch (error) {
                            console.warn('无法生成视频缩略图:', error);
                        }
                    });
                }
            }



            // 生成缩略图
            generateThumbnail();

            // 内联播放优化事件监听
            videoElement.addEventListener('loadstart', function() {
                console.log('开始加载视频');
                // 确保内联播放设置在加载时生效
                optimizeInlinePlayback();
            });

            // 视频元数据加载完成
            videoElement.addEventListener('loadedmetadata', function() {
                console.log('视频元数据加载完成');
                // 再次确保内联播放设置
                optimizeInlinePlayback();
            });

            // 视频数据加载完成
            videoElement.addEventListener('loadeddata', function() {
                console.log('视频数据加载完成');
            });

            // 视频可以播放
            videoElement.addEventListener('canplay', function() {
                console.log('视频可以播放');
            });

            // 播放开始时确保内联播放
            videoElement.addEventListener('play', function() {
                console.log('视频开始播放');
                // 防止移动端自动全屏
                if (/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)) {
                    videoElement.setAttribute('playsinline', 'true');
                    videoElement.setAttribute('webkit-playsinline', 'true');
                }
            });

            // 错误处理
            videoElement.addEventListener('error', function(e) {
                console.error('视频播放出错:', e);

                let errorMessage = '视频加载失败，请检查网络连接。';

                if (videoElement.error) {
                    console.error('错误代码:', videoElement.error.code);
                    console.error('错误信息:', videoElement.error.message);

                    switch (videoElement.error.code) {
                        case 1:
                            errorMessage = '视频加载被中止。';
                            break;
                        case 2:
                            errorMessage = '网络错误，无法加载视频。';
                            break;
                        case 3:
                            errorMessage = '视频解码失败或格式不支持。';
                            break;
                        case 4:
                            errorMessage = '视频不存在或无法访问。';
                            break;
                    }
                }

                // 显示错误信息
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger mt-3';
                errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${errorMessage}`;
                videoElement.parentNode.appendChild(errorDiv);
            });

            // 处理全屏变化事件，确保退出全屏后仍保持内联播放
            document.addEventListener('fullscreenchange', function() {
                if (!document.fullscreenElement) {
                    // 退出全屏后重新应用内联播放设置
                    setTimeout(function() {
                        optimizeInlinePlayback();
                    }, 100);
                }
            });

            // 处理webkit全屏变化
            document.addEventListener('webkitfullscreenchange', function() {
                if (!document.webkitFullscreenElement) {
                    setTimeout(function() {
                        optimizeInlinePlayback();
                    }, 100);
                }
            });

            // 监听页面可见性变化，确保内联播放设置持续有效
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    // 页面重新可见时重新应用设置
                    setTimeout(function() {
                        optimizeInlinePlayback();
                    }, 100);
                }
            });

            // 播放开始事件
            videoElement.addEventListener('play', function() {
                console.log('视频开始播放，ID:', videoId);
            });

            // 视频暂停事件
            videoElement.addEventListener('pause', function() {
                console.log('视频已暂停，ID:', videoId);
            });

            // 视频结束事件
            videoElement.addEventListener('ended', function() {
                console.log('视频播放结束，ID:', videoId);
            });

            console.log('响应式HTML5视频播放器已准备就绪');
        });














    </script>



    <!-- 内联播放优化的视频播放器初始化 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const videoElement = document.getElementById('video-player');

            if (!videoElement) return;

            // 确保内联播放属性设置
            function ensureInlinePlayback() {
                videoElement.setAttribute('playsinline', 'true');
                videoElement.setAttribute('webkit-playsinline', 'true');
                if (/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)) {
                    videoElement.setAttribute('x5-video-player-type', 'h5');
                    videoElement.setAttribute('x5-video-player-fullscreen', 'true');
                    videoElement.setAttribute('x5-video-orientation', 'portraint');
                }
            }

            // 视频加载完成
            videoElement.addEventListener('loadeddata', function() {
                console.log('视频加载完成');
                ensureInlinePlayback();
            });

            // 视频开始播放
            videoElement.addEventListener('play', function() {
                console.log('视频开始播放');
                ensureInlinePlayback();
            });

            // 视频暂停
            videoElement.addEventListener('pause', function() {
                console.log('视频暂停');
            });

            // 视频结束
            videoElement.addEventListener('ended', function() {
                console.log('视频播放结束');
            });

            // 错误处理
            videoElement.addEventListener('error', function(e) {
                console.error('视频播放出错:', e);

                // 显示错误信息
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger mt-3';
                errorDiv.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>视频加载失败，请检查网络连接或稍后重试。';
                videoElement.parentNode.appendChild(errorDiv);
            });

            // 等待事件
            videoElement.addEventListener('waiting', function() {
                console.log('视频缓冲中');
            });

            // 可以播放事件
            videoElement.addEventListener('canplay', function() {
                console.log('视频可以播放');
                ensureInlinePlayback();
            });

            // 初始化时确保内联播放设置
            ensureInlinePlayback();

            console.log('内联播放优化的视频播放器初始化完成');
        });
    </script>
</body>
</html>

