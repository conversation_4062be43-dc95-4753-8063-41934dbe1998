/**
 * 全局样式文件
 */

/* 全局样式 */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    background-color: #ffffff;
    color: #333333;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* 确保容器元素保持白色背景 */
.container, .container-fluid, .row, .col, [class*="col-"] {
    background-color: transparent !important;
    color: inherit !important;
}
    /* color: #333;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
} */

/* 搜索容器样式 */
.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-form {
    display: none !important;
    align-items: center;
    gap: 0.5rem;
    animation: slideIn 0.3s ease-out;
}

.search-form.active {
    display: flex !important;
}

.search-form.show {
    display: flex !important;
}

.search-toggle {
    transition: all 0.3s ease;
}

.search-toggle:hover {
    transform: scale(1.1);
}

/* 搜索输入框样式 */
.search-input-mobile {
    width: 150px;
    font-size: 0.875rem;
    border: 1px solid rgba(255,255,255,0.3);
    background-color: rgba(255,255,255,0.1);
    color: white;
    transition: all 0.3s ease;
}

.search-input-mobile::placeholder {
    color: rgba(255,255,255,0.7);
}

.search-input-mobile:focus {
    background-color: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
    color: white;
    outline: none;
}

/* 搜索框展开动画 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.navbar-nav .nav-link {
    font-size: 0.9rem;
    white-space: nowrap;
    transition: color 0.3s ease;
}



/* 导航栏在移动端不折叠的样式 */
.navbar .d-flex.w-100 {
    flex-wrap: nowrap;
}

.navbar-nav.flex-row .nav-item {
    margin-right: 0.5rem;
}

.navbar-nav.flex-row .nav-link {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}



/* 移动端响应式导航 */
@media (max-width: 768px) {
    .search-input-mobile {
        width: 120px;
        font-size: 0.8rem;
        min-width: 120px;
    }



    .navbar-nav .nav-link {
        font-size: 0.8rem;
        padding: 0.5rem 0.3rem !important;
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }

    .navbar-nav.flex-row .nav-item {
        margin-right: 0.2rem;
    }

    .search-container {
        flex-shrink: 0;
    }

    .search-form {
        gap: 0.25rem;
    }
}

@media (max-width: 576px) {
    .search-input-mobile {
        width: 100px;
        font-size: 0.75rem;
        min-width: 100px;
    }



    .navbar-nav .nav-link {
        font-size: 0.75rem;
        padding: 0.4rem 0.2rem !important;
    }

    .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.75rem;
    }

    .navbar-nav.flex-row .nav-item {
        margin-right: 0.1rem;
    }

    /* 在极小屏幕上隐藏图标文字，只显示图标 */
    .navbar-nav .nav-link i + * {
        display: none;
    }

    .search-form {
        gap: 0.1rem;
    }

    .navbar .d-flex.w-100 {
        flex-wrap: nowrap;
        overflow: hidden;
    }

    /* 搜索框展开时的特殊处理 */
    .search-form.show {
        position: absolute;
        right: 0;
        top: 100%;
        background: rgba(13, 110, 253, 0.95);
        padding: 0.5rem;
        border-radius: 0.375rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        z-index: 1000;
    }
}

/* 通用工具类 */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }
    
    ::-webkit-scrollbar-track {
        background: #2d2d2d;
    }
    
    ::-webkit-scrollbar-thumb {
        background: #555;
    }
    
    ::-webkit-scrollbar-thumb:hover {
        background: #777;
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .btn,
    .alert,
    .modal {
        display: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }
    
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}

/* 无障碍访问 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 主要内容区域样式 */
.main-content, main {
    flex: 1;
    margin-bottom: auto;
}

/* 页脚居中样式和底端定位 */
.bg-dark.text-light.py-4.mt-5 {
    font-size: 15px !important;
    text-align: center;
    padding: 10px !important;
    margin-top: auto !important;
    flex-shrink: 0;
    height: 65px !important;
}

.bg-dark.text-light.py-4.mt-5 .container {
    text-align: center;
}

.bg-dark.text-light.py-4.mt-5 .row {
    justify-content: center;
    text-align: center;
}

.bg-dark.text-light.py-4.mt-5 .col-md-6 {
    text-align: center;
}

.bg-dark.text-light.py-4.mt-5 .text-md-end {
    text-align: center !important;
}

/* video-info 容器内容左对齐 */
.video-info.bg-white.rounded-3.shadow-sm.p-4.mb-4 {
    text-align: left !important;
}

.video-info.bg-white.rounded-3.shadow-sm.p-4.mb-4 * {
    text-align: left !important;
}

/* 焦点样式 */
.btn:focus,
.form-control:focus,
.form-select:focus {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .btn {
        border-width: 2px;
    }
    
    .card {
        border-width: 2px;
    }
    
    .navbar-nav .nav-link {
        font-weight: 600;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ========== 强力防夜间模式样式 ========== */

/* 覆盖所有可能的夜间模式媒体查询 */
@media (prefers-color-scheme: dark) {
    :root {
        color-scheme: light only !important;
        --bs-body-bg: #ffffff !important;
        --bs-body-color: #333333 !important;
    }

    html, body {
        background-color: #ffffff !important;
        color: #333333 !important;
        color-scheme: light only !important;
    }

    /* 强制所有元素保持亮色主题 */
    * {
        background-color: inherit !important;
        color: inherit !important;
        color-scheme: light only !important;
    }

    /* 确保所有容器元素 */
    div, section, article, main, header, footer, nav, aside {
        background-color: transparent !important;
        color: inherit !important;
    }

    /* Bootstrap组件强制亮色 */
    .navbar, .card, .btn, .form-control, .modal, .alert, .dropdown-menu {
        filter: none !important;
        color-scheme: light only !important;
    }

    /* 表格强制亮色 */
    .table, .table th, .table td {
        background-color: inherit !important;
        color: inherit !important;
        border-color: #dee2e6 !important;
    }
}

/* 强制覆盖任何可能的深色主题属性 */
[data-theme="dark"],
[data-bs-theme="dark"],
.dark-theme,
.theme-dark {
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
}

/* 防止浏览器自动应用深色样式 */
@media screen {
    html {
        color-scheme: light only !important;
        background-color: #ffffff !important;
    }

    body {
        background-color: #ffffff !important;
        color: #333333 !important;
    }
}

/* 移动端专用防护 */
@media screen and (max-width: 768px) {
    html, body {
        background-color: #ffffff !important;
        color: #333333 !important;
        color-scheme: light only !important;
    }

    * {
        color-scheme: light only !important;
    }
}
